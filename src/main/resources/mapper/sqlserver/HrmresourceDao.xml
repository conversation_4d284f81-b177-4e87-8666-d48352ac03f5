<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.costanalaysisbackend.dao.sqlserver.HrmresourceDao">
    <resultMap type="com.hj.costanalaysisbackend.entity.sqlserver.Hrmresource" id="HrmresourceMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="loginid" column="loginid" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="lastname" column="lastname" jdbcType="VARCHAR"/>
        <result property="seclevel" column="seclevel" jdbcType="INTEGER"/>
        <result property="departmentid" column="departmentid" jdbcType="INTEGER"/>
        <result property="usekind" column="usekind" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
    </resultMap>

    <!--根据部门ID查询员工-->
    <select id="findByDepartmentId" resultMap="HrmresourceMap">
        SELECT id,
               lastname,
               seclevel,
               departmentid,
               usekind
        FROM [ecology].[dbo].[HrmResource]
        WITH (NOLOCK)
        WHERE departmentid = #{departmentId}
    </select>

    <select id="getByName" resultMap="HrmresourceMap">
        SELECT id,
               lastname,
               departmentid,
               usekind
        FROM [ecology].[dbo].[HrmResource]
        WITH (NOLOCK)
        WHERE lastname like concat('%', #{userName}, '%')
    </select>

    <!--根据登录ID查询用户-->
    <select id="findByLoginId" resultMap="HrmresourceMap">
        SELECT id,
               loginid,
               password,
               lastname,
               seclevel,
               departmentid,
               usekind,
               status
        FROM [ecology].[dbo].[HrmResource]
        WITH (NOLOCK)
        WHERE loginid = #{loginId}
          AND status = 1
    </select>
</mapper>

