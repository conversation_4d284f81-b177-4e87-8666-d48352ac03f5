<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hj.costanalaysisbackend.dao.postgres.PlanWorkHourRecordDao">

    <resultMap type="com.hj.costanalaysisbackend.entity.postgres.PlanWorkHourRecord" id="PlanWorkHourRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="departmentId" column="department_id" jdbcType="INTEGER"/>
        <result property="departmentName" column="department_name" jdbcType="VARCHAR"/>
        <result property="planWorkHour" column="plan_work_hour" jdbcType="NUMERIC"/>
        <result property="date" column="date" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="PlanWorkHourRecordMap">
        select id, department_id, department_name, plan_work_hour, date,type, create_time, update_time
        from plan_work_hour_record
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="PlanWorkHourRecordMap">
        select id,
               department_id,
               department_name,
               plan_work_hour,
               date,
               type,
               create_time,
               update_time
        from plan_work_hour_record
        <where>
            <if test="departmentId != null">
                department_id = #{departmentId}
            </if>
            <if test="departmentName != null and departmentName != ''">
                and department_name like #{departmentName}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="date != null and date != ''">
                and date = #{date}
            </if>
        </where>
        order by date desc
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from plan_work_hour_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="departmentId != null">
                and department_id = #{departmentId}
            </if>
            <if test="departmentName != null and departmentName != ''">
                and department_name = #{departmentName}
            </if>
            <if test="planWorkHour != null">
                and plan_work_hour = #{planWorkHour}
            </if>
            <if test="date != null and date != ''">
                and date = #{date}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into plan_work_hour_record(department_id, department_name, plan_work_hour, date,type, create_time, update_time)
        values (#{departmentId}, #{departmentName}, #{planWorkHour}, #{date},#{type}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into plan_work_hour_record(department_id, department_name, plan_work_hour, date,type, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.departmentId}, #{entity.departmentName}, #{entity.planWorkHour}, #{entity.date},#{entity.type}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into plan_work_hour_record(department_id, department_name, plan_work_hour, date,type, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.departmentId}, #{entity.departmentName}, #{entity.planWorkHour}, #{entity.date},#{entity.type}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on conflict (department_id, date) do update set
            department_name = EXCLUDED.department_name,
            plan_work_hour = EXCLUDED.plan_work_hour,
            update_time = EXCLUDED.update_time
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update plan_work_hour_record
        <set>
            <if test="departmentId != null">
                department_id = #{departmentId},
            </if>
            <if test="departmentName != null and departmentName != ''">
                department_name = #{departmentName},
            </if>
            <if test="planWorkHour != null">
                plan_work_hour = #{planWorkHour},
            </if>
            <if test="date != null and date != ''">
                date = #{date},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from plan_work_hour_record where id = #{id}
    </delete>

    <select id="getPeriodPlanWorkHourForDepartments" resultMap="PlanWorkHourRecordMap">
        select *
        from plan_work_hour_record
        WHERE department_id in
        <foreach item="departmentId" index="index" collection="departmentIds" open="(" separator="," close=")">
            #{departmentId}
        </foreach>
        and date &gt;= #{startDate}
        and date &lt;= #{endDate}
        order by date asc
    </select>

    <select id="findBySameDate" resultMap="PlanWorkHourRecordMap">
        select *
        from plan_work_hour_record
        WHERE department_id = #{departmentId}
        and date = #{date}
        and type = #{type}
        <if test="id != null">
            and id != #{id}
        </if>
        limit 1
    </select>

</mapper>
