server:
  port: 8100
spring:
  application:
    name: cost-analysis-backend
  # 多数据源配置
  datasource:
    # 主数据源 - SQL Server
    primary:
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      jdbc-url: *************************************************************************************************
      username: sa
      password: Hj@20201001
    # PostgreSQL数据源
    postgresql:
      driver-class-name: org.postgresql.Driver
      jdbc-url: **************************************
      username: postgres
      password: hj@20250421
  # Quartz配置
  quartz:
    job-store-type: memory # 使用内存存储任务信息
    scheduler-name: LaborCostScheduler
    auto-startup: true # 自动启动调度器
    startup-delay: 5s # 延迟5秒启动调度器
    wait-for-jobs-to-complete-on-shutdown: true # 关闭时等待任务完成
    overwrite-existing-jobs: true # 覆盖已存在的任务
    properties:
      org.quartz.threadPool.threadCount: 5 # 线程池大小
pagehelper:
  helperDialect: postgresql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
wx-work:
  sendMsgUrl: http://192.168.1.48:9997/wx-server/sendMarkdownMsg/cost-analysis
  enableIdTrans: 0
  enableDuplicateCheck: 0
  duplicateCheckInterval: 1800
  safe: 0
  agentId: 1000219
  costAnalysisUrl: https://itsm.hongjing-wh.com:18975
  costAnalysisInputUrl: https://itsm.hongjing-wh.com:18975/cost
  planWorkHourInputUrl: https://itsm.hongjing-wh.com:18975/plan

# JWT配置
jwt:
  secret: cost-analysis-backend-jwt-secret-key-2024-very-long-secret
  expiration: 604800000 # 7天，单位：毫秒

# 日志配置
logging:
  level:
    root: info
    com.hj.costanalaysisbackend: debug
  file:
    path: ./logs
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
