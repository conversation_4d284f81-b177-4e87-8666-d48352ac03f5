package com.hj.costanalaysisbackend.controller;

import com.hj.costanalaysisbackend.annotation.RequireLogin;
import com.hj.costanalaysisbackend.common.CommonResult;
import com.hj.costanalaysisbackend.util.UserContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于测试登录功能和权限控制
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {
    
    /**
     * 公开接口，不需要登录
     */
    @GetMapping("/public")
    @RequireLogin(false)
    public CommonResult<Map<String, Object>> publicApi() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这是一个公开接口，不需要登录");
        result.put("timestamp", LocalDateTime.now());
        return CommonResult.ok(result);
    }
    
    /**
     * 需要登录的接口
     */
    @GetMapping("/private")
    @RequireLogin
    public CommonResult<Map<String, Object>> privateApi() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这是一个需要登录的接口");
        result.put("timestamp", LocalDateTime.now());
        result.put("currentUser", UserContext.getCurrentUser());
        return CommonResult.ok(result);
    }
    
    /**
     * 默认需要登录的接口（没有注解，根据拦截器配置决定）
     */
    @GetMapping("/default")
    public CommonResult<Map<String, Object>> defaultApi() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "这是一个默认接口，根据拦截器配置决定是否需要登录");
        result.put("timestamp", LocalDateTime.now());
        result.put("currentUser", UserContext.getCurrentUser());
        return CommonResult.ok(result);
    }
}
