package com.hj.costanalaysisbackend.controller;

import com.hj.costanalaysisbackend.annotation.RequireLogin;
import com.hj.costanalaysisbackend.common.CommonResult;
import com.hj.costanalaysisbackend.common.PageRequest;
import com.hj.costanalaysisbackend.common.PageResult;
import com.hj.costanalaysisbackend.entity.postgres.PlanWorkHourRecord;
import com.hj.costanalaysisbackend.exception.CommonException;
import com.hj.costanalaysisbackend.service.PlanWorkHourRecordService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("planWorkHourRecord")
@RequireLogin // 整个Controller都需要登录
public class PlanWorkHourRecordController {
    /**
     * 服务对象
     */
    @Resource
    private PlanWorkHourRecordService planWorkHourRecordService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @PostMapping("/page")
    public CommonResult<PageResult<PlanWorkHourRecord>> queryByPage(
            @RequestBody PageRequest<PlanWorkHourRecord> pageRequest) {
        return CommonResult.ok(this.planWorkHourRecordService.queryByPage(pageRequest));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    public CommonResult<PlanWorkHourRecord> queryById(@PathVariable("id") Long id) {
        return CommonResult.ok(this.planWorkHourRecordService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param planWorkHourRecord 实体
     * @return 新增结果
     */
    @PostMapping
    public CommonResult<PlanWorkHourRecord> add(@RequestBody PlanWorkHourRecord planWorkHourRecord) {
        // 参数校验
        if (planWorkHourRecord.getDepartmentId() == null) {
            throw new CommonException("部门ID不能为空");
        }
        if (planWorkHourRecord.getPlanWorkHour() == null) {
            throw new CommonException("计划工时不能为空");
        }
        if (planWorkHourRecord.getType() == null) {
            throw new CommonException("员工类型不能为空");
        }
        if (planWorkHourRecord.getDate() == null || planWorkHourRecord.getDate().isEmpty()) {
            throw new CommonException("日期不能为空");
        }

        // 先检查是否有相同日期的记录
        PlanWorkHourRecord existingRecord = planWorkHourRecordService.handleDateDuplicate(planWorkHourRecord);
        if (existingRecord != null) {
            // 如果有相同日期的记录，直接更新该记录
            existingRecord.setPlanWorkHour(planWorkHourRecord.getPlanWorkHour());
            return CommonResult.ok(this.planWorkHourRecordService.update(existingRecord));
        }

        // 插入数据库并返回结果
        return CommonResult.ok(this.planWorkHourRecordService.insert(planWorkHourRecord));
    }

    /**
     * 编辑数据
     *
     * @param planWorkHourRecord 实体
     * @return 编辑结果
     */
    @PutMapping
    public CommonResult<PlanWorkHourRecord> edit(@RequestBody PlanWorkHourRecord planWorkHourRecord) {
        // 参数校验
        if (planWorkHourRecord.getId() == null) {
            throw new CommonException("ID不能为空");
        }
        if (planWorkHourRecord.getDepartmentId() == null) {
            throw new CommonException("部门ID不能为空");
        }
        if (planWorkHourRecord.getType() == null) {
            throw new CommonException("员工类型不能为空");
        }
        if (planWorkHourRecord.getPlanWorkHour() == null) {
            throw new CommonException("计划工时不能为空");
        }
        if (planWorkHourRecord.getDate() == null || planWorkHourRecord.getDate().isEmpty()) {
            throw new CommonException("日期不能为空");
        }

        // 先检查是否有相同日期的记录（排除自身）
        PlanWorkHourRecord existingRecord = planWorkHourRecordService.handleDateDuplicate(planWorkHourRecord);
        if (existingRecord != null) {
            // 如果有相同日期的记录，删除该记录，然后更新当前记录
            planWorkHourRecordService.deleteById(existingRecord.getId());
        }

        return CommonResult.ok(this.planWorkHourRecordService.update(planWorkHourRecord));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> deleteById(@PathVariable("id") Long id) {
        return CommonResult.ok(this.planWorkHourRecordService.deleteById(id));
    }
}
