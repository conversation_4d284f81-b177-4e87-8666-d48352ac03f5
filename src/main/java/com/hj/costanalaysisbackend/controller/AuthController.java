package com.hj.costanalaysisbackend.controller;

import com.hj.costanalaysisbackend.annotation.RequireLogin;
import com.hj.costanalaysisbackend.common.CommonResult;
import com.hj.costanalaysisbackend.dto.LoginRequest;
import com.hj.costanalaysisbackend.dto.LoginResponse;
import com.hj.costanalaysisbackend.dto.UserInfo;
import com.hj.costanalaysisbackend.service.AuthService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Resource
    private AuthService authService;
    
    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    @PostMapping("/login")
    @RequireLogin(false) // 登录接口不需要认证
    public CommonResult<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        LoginResponse response = authService.login(loginRequest);
        return CommonResult.ok(response);
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("/userinfo")
    @RequireLogin // 需要登录
    public CommonResult<UserInfo> getUserInfo() {
        UserInfo userInfo = authService.getCurrentUserInfo();
        return CommonResult.ok(userInfo);
    }
    
    /**
     * 用户登出
     * 
     * @return 响应结果
     */
    @PostMapping("/logout")
    @RequireLogin // 需要登录
    public CommonResult<String> logout() {
        authService.logout();
        return CommonResult.ok("退出登录成功");
    }
}
