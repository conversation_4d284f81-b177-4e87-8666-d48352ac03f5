package com.hj.costanalaysisbackend.controller;

import com.hj.costanalaysisbackend.common.CommonResult;
import com.hj.costanalaysisbackend.service.PushWxWorkService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;


@RestController
@RequestMapping("pushWxWork")
public class PushWxWorkController {
    private static final Logger logger = LoggerFactory.getLogger(PushWxWorkController.class);

    /**
     * 服务对象
     */
    @Resource
    private PushWxWorkService pushWxWorkService;

    /**
     * 自动在企业微信上每天早上8点准时推送，内容包含：昨日人工成本，当月累计人工成本，累计收入，累计成本，收入结余
     *
     * @return 获取结果
     */
    @GetMapping("/dailyAnalysis")
    public CommonResult<Map<String, Object>> dailyAnalysis() {
        logger.info("企业微信每日提醒数据获取");
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        // 结束日期设置为今天
        String endDate = dayFormat.format(calendar.getTime());
        // 开始日期设置为该月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        String startDate = dayFormat.format(calendar.getTime());
        Map<String, Object> result = pushWxWorkService.dailyAnalysisPush(191, startDate, endDate);
        return CommonResult.ok(result);
    }

    @GetMapping("/dailyInputIncomeAndCost")
    public CommonResult<String> dailyInputIncomeAndCost() {
        logger.info("企业微信每日提醒收入支出录入");
        pushWxWorkService.dailyInputIncomeAndCostPush();
        return CommonResult.ok();
    }

    @GetMapping("/dailyInputPlanWorkHourPush")
    public CommonResult<String> dailyInputPlanWorkHourPush() {
        logger.info("企业微信每日计划工时录入");
        pushWxWorkService.dailyInputPlanWorkHourPush();
        return CommonResult.ok();
    }

}

