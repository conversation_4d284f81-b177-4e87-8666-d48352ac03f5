package com.hj.costanalaysisbackend.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hj.costanalaysisbackend.annotation.RequireLogin;
import com.hj.costanalaysisbackend.common.CommonResult;
import com.hj.costanalaysisbackend.common.ResponseCode;
import com.hj.costanalaysisbackend.dto.UserInfo;
import com.hj.costanalaysisbackend.util.JwtUtil;
import com.hj.costanalaysisbackend.util.UserContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.lang.reflect.Method;

/**
 * 登录拦截器
 * 
 * <AUTHOR>
 */
@Component
public class LoginInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginInterceptor.class);
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是方法处理器，直接放行
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();
        
        // 检查方法或类上是否有@RequireLogin注解
        RequireLogin methodAnnotation = method.getAnnotation(RequireLogin.class);
        RequireLogin classAnnotation = clazz.getAnnotation(RequireLogin.class);
        
        // 如果方法上有注解，以方法注解为准
        boolean requireLogin = true;
        if (methodAnnotation != null) {
            requireLogin = methodAnnotation.value();
        } else if (classAnnotation != null) {
            requireLogin = classAnnotation.value();
        } else {
            // 如果没有注解，默认不需要登录
            requireLogin = false;
        }
        
        // 如果不需要登录，直接放行
        if (!requireLogin) {
            return true;
        }
        
        // 获取Authorization头
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            logger.warn("请求{}缺少Authorization头或格式错误", request.getRequestURI());
            writeUnauthorizedResponse(response, "缺少认证信息");
            return false;
        }
        
        // 提取JWT令牌
        String token = authHeader.substring(7);
        
        // 验证JWT令牌
        if (!jwtUtil.validateToken(token)) {
            logger.warn("请求{}的JWT令牌无效", request.getRequestURI());
            writeUnauthorizedResponse(response, "认证信息无效或已过期");
            return false;
        }
        
        // 从JWT令牌中获取用户信息
        UserInfo userInfo = jwtUtil.getUserInfoFromToken(token);
        if (userInfo == null) {
            logger.warn("请求{}无法从JWT令牌中获取用户信息", request.getRequestURI());
            writeUnauthorizedResponse(response, "无法获取用户信息");
            return false;
        }
        
        // 将用户信息存储到线程上下文中
        UserContext.setCurrentUser(userInfo);
        
        logger.debug("用户{}({})访问{}", userInfo.getRealName(), userInfo.getUsername(), request.getRequestURI());
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除线程上下文中的用户信息
        UserContext.clear();
    }
    
    /**
     * 写入未授权响应
     */
    private void writeUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        CommonResult<String> result = new CommonResult<>(ResponseCode.UNAUTHORIZED.getCode(), message, null);
        String jsonResponse = objectMapper.writeValueAsString(result);
        
        response.getWriter().write(jsonResponse);
    }
}
