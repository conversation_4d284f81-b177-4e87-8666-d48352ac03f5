package com.hj.costanalaysisbackend.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.hj.costanalaysisbackend.config.WxWorkConfig;
import com.hj.costanalaysisbackend.dao.postgres.DepartmentMonthCostDailyRecordDao;
import com.hj.costanalaysisbackend.dao.postgres.WorkDayRecordDao;
import com.hj.costanalaysisbackend.dao.postgres.WorkHourStatisticsConfigDao;
import com.hj.costanalaysisbackend.dao.sqlserver.HrmdepartmentDao;
import com.hj.costanalaysisbackend.entity.postgres.*;
import com.hj.costanalaysisbackend.entity.sqlserver.Hrmdepartment;
import com.hj.costanalaysisbackend.entity.wxwork.Markdown;
import com.hj.costanalaysisbackend.service.IncomeExtraCostRecordService;
import com.hj.costanalaysisbackend.service.PushConfigService;
import com.hj.costanalaysisbackend.service.PushWxWorkService;
import com.hj.costanalaysisbackend.vo.DepartmentMonthCostVo;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 提醒服务实现类
 */
@Service
public class PushWxWorkServiceImpl implements PushWxWorkService {

    private static final Logger logger = LoggerFactory.getLogger(PushWxWorkServiceImpl.class);

    @Resource
    private HrmdepartmentDao hrmdepartmentDao;

    @Resource
    private DepartmentMonthCostDailyRecordDao departmentMonthCostDailyRecordDao;

    @Resource
    private IncomeExtraCostRecordService incomeExtraCostRecordService;

    @Resource
    private CostAnalysisServiceImpl costAnalysisService;

    @Resource
    private WxWorkConfig wxWorkConfig;

    @Resource
    private PushConfigService pushConfigService;

    @Resource
    private WorkDayRecordDao workDayRecordDao;

    @Resource
    private WorkHourStatisticsConfigDao workHourStatisticsConfigDao;

    @Override
    public Map<String, Object> dailyAnalysisPush(Integer departmentId, String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        // 获取部门信息
        List<Hrmdepartment> allDepartments = this.hrmdepartmentDao.queryAll();
        Hrmdepartment department = null;
        for (Hrmdepartment dept : allDepartments) {
            if (dept.getId().equals(departmentId)) {
                department = dept;
                break;
            }
        }
        if (department == null) {
            return result;
        }

        // 递归查询出该部门的所有子部门
        List<Hrmdepartment> childDepartments = costAnalysisService.findAllChildDepartments(departmentId, allDepartments);

        // 获取当前月份
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");
        String thisMonth = monthFormat.format(new Date());

        // 查询本月成本数据
        List<DepartmentMonthCost> thisMonthUtilTodayCosts = costAnalysisService.getDepartmentCostsWithChildrenForMonth(
                departmentId, childDepartments, thisMonth, thisMonth);

        // 查询需要统计实际工时的部门
        List<WorkHourStatisticsConfig> workHourStatisticsConfigs = workHourStatisticsConfigDao.queryAllByLimit(null, null, null);
        List<Integer> frontLineStatisticsDeptIds = workHourStatisticsConfigs.stream().filter(w -> w.getType() == 1).map(WorkHourStatisticsConfig::getDepartmentId).toList();
        List<Integer> secondTierStatisticsDeptIds = workHourStatisticsConfigs.stream().filter(w -> w.getType() == 2).map(WorkHourStatisticsConfig::getDepartmentId).toList();

        // 当月累计人工成本
        DepartmentMonthCostVo thisMonthCost = costAnalysisService.calculateCost(thisMonthUtilTodayCosts, frontLineStatisticsDeptIds, secondTierStatisticsDeptIds);

        // 统计月初到今天的工作日数量
        WorkDayRecord workDayRecord = workDayRecordDao.findByMonthAndType(thisMonth, 0);
        int workDaysPassed = workDayRecord.getWorkDays() == null ? 1 : workDayRecord.getWorkDays();
        // 计算平均工作日成本
        double averageWorkDailyCost = thisMonthCost.getPeriodTotalCost() / workDaysPassed;
        // 计算本月预测成本 预测本月总成本 = 平均工作日成本 × 本月总工作日天数
        workDayRecord = workDayRecordDao.findByMonthAndType(thisMonth, 1);
        int workDaysThisMonth = workDayRecord.getWorkDays() == null ? 1 : workDayRecord.getWorkDays();
        double predictedMonthlyCost = (int) Math.round(averageWorkDailyCost * workDaysThisMonth);
        // 统计本月至今总成本
        Calendar today = Calendar.getInstance();
        double coefficient = (double) (today.get(Calendar.DAY_OF_MONTH) - 1) / (double) today.getActualMaximum(Calendar.DAY_OF_MONTH);
        thisMonthCost.setPeriodTotalCost(predictedMonthlyCost * coefficient);
        result.put("laborCost", NumberUtil.decimalFormat(",###.##", thisMonthCost.getPeriodTotalCost() / 10000));

        // 当月累计收入
        List<IncomeExtraCostRecord> incomeList = incomeExtraCostRecordService.getPeriodIncome(departmentId, startDate, endDate);
        double income = 0.0;
        if (!CollectionUtils.isEmpty(incomeList)) {
            income = incomeList.stream().mapToDouble(IncomeExtraCostRecord::getAmount).sum();
        }
        result.put("totalIncome", NumberUtil.decimalFormat(",###.##", income / 10000));
        // 当月累计成本
        List<IncomeExtraCostRecord> extraCostList = incomeExtraCostRecordService.getPeriodExtraCost(departmentId, startDate, endDate);
        double extraCost = 0.0;
        if (!CollectionUtils.isEmpty(extraCostList)) {
            extraCost = extraCostList.stream().mapToDouble(IncomeExtraCostRecord::getAmount).sum();
        }
        result.put("totalCost", NumberUtil.decimalFormat(",###.##", (thisMonthCost.getPeriodTotalCost() + extraCost) / 10000));
        // 当月收入结余
        result.put("incomeSurplus", NumberUtil.decimalFormat(",###.##", (income - thisMonthCost.getPeriodTotalCost() - extraCost) / 10000));

        // 昨日考勤成本 = 昨天的成本记录 - 前天的成本记录
        result.put("yesterdayAttendanceCost", 0.0);
        // 日均成本
        result.put("averageCost", 0.0);
        // 收集所有部门ID
        List<Integer> allDepartmentIds = new ArrayList<>();
        allDepartmentIds.add(departmentId);
        for (Hrmdepartment childDept : childDepartments) {
            allDepartmentIds.add(childDept.getId());
        }
        // 昨天的成本记录
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - 1);
        SimpleDateFormat dateformat = new SimpleDateFormat("yyyy-MM-dd");
        String yesterday = dateformat.format(calendar.getTime());
        List<DepartmentMonthCostDailyRecord> yesterdayRecord = departmentMonthCostDailyRecordDao.findByDepartmentIdsForMonthDate(allDepartmentIds, yesterday, yesterday.substring(0, 7));
        // 如果昨天没有成本则返回0
        if (CollectionUtils.isEmpty(yesterdayRecord)) {
            return result;
        }
        //如果今天不是月初第一天，则需获取前天成本记录
        double dayBeforeYesterdayCost = 0.0;
        if (calendar.get(Calendar.DAY_OF_MONTH) != 1) {
            // 前天的成本记录
            calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - 1);
            String dayBeforeYesterday = dateformat.format(calendar.getTime());
            List<DepartmentMonthCostDailyRecord> dayBeforeYesterdayRecord = departmentMonthCostDailyRecordDao.findByDepartmentIdsForMonthDate(allDepartmentIds, dayBeforeYesterday, dayBeforeYesterday.substring(0, 7));
            // 如果前天没有成本则返回0
            if (CollectionUtils.isEmpty(dayBeforeYesterdayRecord)) {
                return result;
            }
            for (DepartmentMonthCostDailyRecord record : dayBeforeYesterdayRecord) {
                dayBeforeYesterdayCost += record.getFixedCost() + record.getFrontlineFormalOverTimeCost() + record.getSecondTierOverTimeCost() + record.getNightAllowance();
            }
        }
        double yesterdayCost = 0.0;
        for (DepartmentMonthCostDailyRecord record : yesterdayRecord) {
            yesterdayCost += record.getFixedCost() + record.getFrontlineFormalOverTimeCost() + record.getSecondTierOverTimeCost() + record.getNightAllowance();
        }
        result.put("yesterdayAttendanceCost", NumberUtil.decimalFormat(",###.##", (yesterdayCost - dayBeforeYesterdayCost) / 10000));

        // 计算当月日均人工成本
//        coefficient = thisMonthCost.getPeriodTotalCost() / yesterdayCost;
//        double averageCost = (yesterdayCost * coefficient) / calendar.get(Calendar.DAY_OF_MONTH);

        // 平均日预测成本=本月预测成本/本月总天数
        int daysInMonth = Calendar.getInstance().getActualMaximum(Calendar.DAY_OF_MONTH);
        double averageDailyCost = predictedMonthlyCost / daysInMonth;

        result.put("averageCost", NumberUtil.decimalFormat(",###.##", averageDailyCost / 10000));

        List<PushConfig> pushConfigs = pushConfigService.queryByType(1);
        String toUser = pushConfigs.stream().map(PushConfig::getUserId).map(String::valueOf).collect(Collectors.joining("|"));
        String url = wxWorkConfig.getCostAnalysisUrl();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        calendar.add(Calendar.DAY_OF_MONTH, 1);

        String content =
                "### 📊 数据概览：\n\n" +
                        "> 📅 **昨日日期：** " + dateFormat.format(calendar.getTime()) + "\n\n" +
                        "> 💼 **昨日考勤人工成本：** **" + result.get("yesterdayAttendanceCost") + "万元**\n\n" +
                        "> 👤 **日均人工成本：** **" + result.get("averageCost") + "万元**\n\n" +
                        "> 📔 **当月累计人工成本：** **" + result.get("laborCost") + "万元**\n\n" +
                        "> 📉 **当月累计总成本：** **" + result.get("totalCost") + "万元**\n\n" +
                        "> 💰 **当月累计收入：** **" + result.get("totalIncome") + "万元**\n\n" +
                        "> 📈 **当月收入结余：** **" + result.get("incomeSurplus") + "万元**\n\n" +
                        "---\n" +
                        "🔗  [查看详情](" + url + ")";


        Markdown markdown = new Markdown(toUser, null, null, "markdown", content, wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
        sendWxWorkMsg(JSONUtil.toJsonStr(markdown));

        return result;
    }

    @Override
    public void dailyInputIncomeAndCostPush() {
        logger.info("开始执行每日收入和成本录入提醒推送");

        List<PushConfig> pushConfigs = pushConfigService.queryByType(2);
        String toUser = pushConfigs.stream().map(PushConfig::getUserId).map(String::valueOf).collect(Collectors.joining("|"));
        String url = wxWorkConfig.getCostAnalysisInputUrl();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        Calendar calendar = Calendar.getInstance();

        String content =
                "### 📝 收支录入提醒：\n\n" +
                        "> 📅 **日期：** " + dateFormat.format(calendar.getTime()) + "\n\n" +
                        "> ⏰ **请及时录入收入和其他支出数据**\n\n" +
                        "> 📊 **及时录入数据有助于更准确地分析成本和收入情况**\n\n" +
                        "---\n" +
                        "🔗  [点击进入录入页面](" + url + ")";

        Markdown markdown = new Markdown(toUser, null, null, "markdown", content, wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
        sendWxWorkMsg(JSONUtil.toJsonStr(markdown));

        logger.info("每日收入和成本录入提醒推送执行完成");
    }

    @Override
    public void dailyInputPlanWorkHourPush() {
        logger.info("开始执行每日计划工时录入提醒推送");

        List<PushConfig> pushConfigs = pushConfigService.queryByType(3);
        String toUser = pushConfigs.stream().map(PushConfig::getUserId).map(String::valueOf).collect(Collectors.joining("|"));
        String url = wxWorkConfig.getPlanWorkHourInputUrl();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        Calendar calendar = Calendar.getInstance();

        String content =
                "### 📝 每日计划工时录入提醒：\n\n" +
                        "> 📅 **日期：** " + dateFormat.format(calendar.getTime()) + "\n\n" +
                        "> ⏰ **请及时录入计划工时数据**\n\n" +
                        "> 📊 **及时录入数据有助于工厂提高生产效率**\n\n" +
                        "---\n" +
                        "🔗  [点击进入录入页面](" + url + ")";

        Markdown markdown = new Markdown(toUser, null, null, "markdown", content, wxWorkConfig.getEnableIdTrans(), wxWorkConfig);
        sendWxWorkMsg(JSONUtil.toJsonStr(markdown));

        logger.info("每日计划工时录入提醒推送执行完成");
    }

    private void sendWxWorkMsg(String msg) {
        try {
            String apiUrl = wxWorkConfig.getSendMsgUrl();
            logger.info("发送企业微信消息到：{}", apiUrl);

            String result = HttpUtil.createPost(apiUrl).contentType("application/json")
                    .body(msg)
                    .execute()
                    .body();

            logger.info("发送企业微信消息返回结果：{}", result);
        } catch (Exception e) {
            logger.error("发送企业微信消息失败", e);
            throw e; // 重新抛出异常，以便重试机制可以捕获并处理
        }
    }

}
