package com.hj.costanalaysisbackend.service.impl;

import com.hj.costanalaysisbackend.dao.postgres.DepartmentMonthCostDailyRecordDao;
import com.hj.costanalaysisbackend.dao.postgres.DepartmentMonthCostDao;
import com.hj.costanalaysisbackend.dao.sqlserver.HrmdepartmentDao;
import com.hj.costanalaysisbackend.entity.postgres.DepartmentMonthCost;
import com.hj.costanalaysisbackend.entity.postgres.DepartmentMonthCostDailyRecord;
import com.hj.costanalaysisbackend.entity.postgres.HourlyAverageCost;
import com.hj.costanalaysisbackend.entity.sqlserver.Hrmdepartment;
import com.hj.costanalaysisbackend.service.DepartmentMonthCostService;
import com.hj.costanalaysisbackend.service.HourlyAverageCostService;
import com.hj.costanalaysisbackend.util.RetryUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;


@Service("DepartmentMonthCostService")
public class DepartmentMonthCostServiceImpl implements DepartmentMonthCostService {
    private static final Logger logger = LoggerFactory.getLogger(DepartmentMonthCostServiceImpl.class);

    @Resource
    private DepartmentMonthCostDao DepartmentMonthCostDao;

    @Resource
    private HrmdepartmentDao hrmdepartmentDao;

    @Resource
    private HourlyAverageCostService hourlyAverageCostService;

    @Resource
    private StoredProcedureServiceImpl storedProcedureService;

    @Resource
    private DepartmentMonthCostDailyRecordDao departmentMonthCostDailyRecordDao;


    @Override
    public Map<String, Object> executeManually(String year, String month, String departmentId) {
        Map<String, Object> response = new HashMap<>();

        String dateStr;
        if (month.length() == 1) {
            dateStr = year + "-0" + month;
        } else {
            dateStr = year + "-" + month;
        }

        // 从average_cost表获取type=1和type=2且createTime是今年的最新数据
        HourlyAverageCost frontlineFormal = hourlyAverageCostService.findLatestByDepartmentIdAndType(null, 1, dateStr); // 一线正式工
        HourlyAverageCost frontlineLabor = hourlyAverageCostService.findLatestByDepartmentIdAndType(null, 2, dateStr); // 一线劳务工
        HourlyAverageCost holidayOverTime = hourlyAverageCostService.findLatestByDepartmentIdAndType(null, 4, dateStr); // 节假日加班费
        HourlyAverageCost normalOverTime = hourlyAverageCostService.findLatestByDepartmentIdAndType(null, 5, dateStr); // 平时加班费
        HourlyAverageCost weekendOverTime = hourlyAverageCostService.findLatestByDepartmentIdAndType(null, 6, dateStr); // 周末加班费

        if (frontlineFormal == null || frontlineLabor == null) {
            String errorMsg = "没有设置一线员工成本标准，无法计算";
            logger.error(errorMsg);
            response.put("success", false);
            response.put("message", errorMsg);
            return response;
        }

        // 所有部门信息
        List<Hrmdepartment> departments = hrmdepartmentDao.queryAll();

        try {
            // 每次都统计/更新本月的数据，调用execute方法
            int successCount = 0;
            int failCount = 0;

            // 本月所有正式工的考勤数据 - 使用重试机制
            Map<String, Map<String, Map<String, Object>>> formalAttendanceMap;
            try {
                formalAttendanceMap = RetryUtil.executeWithDeadlockRetry(
                    () -> storedProcedureService.callJmbbTestProcedure(year, month, departmentId, "3"),
                    "调用存储过程获取正式工考勤数据"
                );
            } catch (Exception e) {
                logger.error("获取正式工考勤数据失败，年月：{}-{}，部门ID：{}", year, month, departmentId, e);
                throw new RuntimeException("获取正式工考勤数据失败: " + e.getMessage(), e);
            }

            // 本月所有劳务工的考勤数据 - 使用重试机制
            Map<String, Map<String, Map<String, Object>>> laborAttendanceMap;
            try {
                laborAttendanceMap = RetryUtil.executeWithDeadlockRetry(
                    () -> storedProcedureService.callJmbbTestProcedure(year, month, departmentId, "4"),
                    "调用存储过程获取劳务工考勤数据"
                );
            } catch (Exception e) {
                logger.error("获取劳务工考勤数据失败，年月：{}-{}，部门ID：{}", year, month, departmentId, e);
                throw new RuntimeException("获取劳务工考勤数据失败: " + e.getMessage(), e);
            }
            // 所有二线员工最近上传的正常工作时均成本
            List<HourlyAverageCost> secondTierCostList = hourlyAverageCostService.findAllSecondTierCost(dateStr);
            try {
                Map<String, Object> result = execute(departments, secondTierCostList, dateStr, frontlineFormal, frontlineLabor, holidayOverTime, normalOverTime,
                        weekendOverTime, formalAttendanceMap, laborAttendanceMap);
                if (Boolean.TRUE.equals(result.get("success"))) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                logger.error("计算日期 {} 的成本时发生错误: {}", year + "-" + month, e.getMessage());
                failCount++;
            }

            String resultMsg = String.format("计算完成，共计算%d月，成功%d月，失败%d月",
                    successCount + failCount, successCount, failCount);
            logger.info(resultMsg);

            response.put("success", true);
            response.put("message", resultMsg);
            response.put("successCount", successCount);
            response.put("failCount", failCount);

            return response;

        } catch (Exception e) {
            String errorMsg = "执行部门月成本统计时发生错误: " + e.getMessage();
            logger.error(errorMsg, e);
            response.put("success", false);
            response.put("message", errorMsg);
            return response;
        }
    }

    private Map<String, Object> execute(List<Hrmdepartment> departments, List<HourlyAverageCost> secondTierCostList, String dateStr,
                                        HourlyAverageCost frontlineFormal, HourlyAverageCost frontlineLabor, HourlyAverageCost holidayOverTime,
                                        HourlyAverageCost normalOverTime, HourlyAverageCost weekendOverTime,
                                        Map<String, Map<String, Map<String, Object>>> formalAttendanceMap, Map<String, Map<String, Map<String, Object>>> laborAttendanceMap) {

        Map<String, Object> response = new HashMap<>();

        logger.info("开始执行部门月成本统计，月份: {}", dateStr);
        try {
            // 查询所有有效部门
            if (departments == null || departments.isEmpty()) {
                response.put("success", false);
                response.put("message", "未查询到部门信息");
                response.put("date", dateStr);
                return response;
            }

            // 部门ID与部门对象的映射，用于快速查找
            Map<Integer, Hrmdepartment> departmentMap = new HashMap<>();
            for (Hrmdepartment department : departments) {
                departmentMap.put(department.getId(), department);
            }

            // 统计变量
            AtomicInteger processedDepartments = new AtomicInteger(0);
            AtomicInteger formalEmployeeCount = new AtomicInteger(0);
            AtomicInteger laborEmployeeCount = new AtomicInteger(0);
            AtomicInteger totalCost = new AtomicInteger(0);

            // 遍历所有部门，计算每个部门的月成本
            // 查询部门的成本标准
            Map<Integer, Double> secondTierMap = new HashMap<>();
            secondTierCostList.forEach(cost -> secondTierMap.put(cost.getDepartmentId(), cost.getAverageCost()));
            for (Hrmdepartment department : departments) {
                // 部门考勤数据
                Map<String, Map<String, Object>> formalAttendanceMapByResourceId = formalAttendanceMap.get(String.valueOf(department.getId()));
                Map<String, Map<String, Object>> laborAttendanceMapByResourceId = laborAttendanceMap.get(String.valueOf(department.getId()));
                if (null == formalAttendanceMapByResourceId && null == laborAttendanceMapByResourceId) {
                    continue;
                }
                //  处理部门
                processDepartment(formalEmployeeCount, laborEmployeeCount, department, departmentMap, dateStr, processedDepartments, totalCost, frontlineFormal,
                        frontlineLabor, holidayOverTime, normalOverTime, weekendOverTime, secondTierMap, formalAttendanceMapByResourceId, laborAttendanceMapByResourceId);
            }

            String result = String.format("部门月成本统计完成，日期：%s，共处理%d个部门，总成本：%d元，正式员工%d人，劳务工%d人", dateStr,
                    processedDepartments.get(), totalCost.get(), formalEmployeeCount.get(), laborEmployeeCount.get());
            logger.info(result);

            response.put("success", true);
            response.put("message", result);
            response.put("date", dateStr);
            return response;

        } catch (Exception e) {
            String errorMsg = "部门月成本统计执行异常: " + e.getMessage();
            logger.error(errorMsg, e);
            response.put("success", false);
            response.put("message", errorMsg);
            response.put("date", dateStr);
            return response;
        }
    }

    /**
     * 处理单个部门的成本统计
     */
    private void processDepartment(AtomicInteger formalEmployeeCount, AtomicInteger laborEmployeeCount,
                                   Hrmdepartment department, Map<Integer, Hrmdepartment> departmentMap,
                                   String dateStr, AtomicInteger processedDepartments, AtomicInteger totalCost,
                                   HourlyAverageCost frontlineFormal, HourlyAverageCost frontlineLabor,
                                   HourlyAverageCost holidayOverTime, HourlyAverageCost normalOverTime, HourlyAverageCost weekendOverTime,
                                   Map<Integer, Double> secondTierMap,
                                   Map<String, Map<String, Object>> formalAttendanceMapByResourceId,
                                   Map<String, Map<String, Object>> laborAttendanceMapByResourceId) {
        Integer departmentId = department.getId();

        // 查询部门的成本标准
        Double secondTier = secondTierMap.get(departmentId);

        // 如果该部门没有配置二线员工成本标准，则递归查询上级部门的成本标准
        if (secondTier == null) {
            logger.info("部门[{}]没有设置二线员工成本标准，尝试查询上级部门", department.getDepartmentname());

            // 获取上级部门ID
            Integer supDepId = department.getSupdepid();

            // 如果有上级部门，则递归查询上级部门的成本标准
            while (supDepId != null && supDepId > 0) {
                Hrmdepartment parentDept = departmentMap.get(supDepId);
                if (parentDept != null) {
                    logger.info("尝试使用上级部门[{}]的成本标准", parentDept.getDepartmentname());
                    secondTier = secondTierMap.get(parentDept.getId());
                    if (secondTier != null) {
                        logger.info("使用上级部门[{}]的成本标准", parentDept.getDepartmentname());
                        break;
                    }
                    // 继续向上查找
                    supDepId = parentDept.getSupdepid();
                } else {
                    break;
                }
            }
        }

        // 检查是否有所需的所有成本标准
        if (frontlineFormal == null || frontlineLabor == null || secondTier == null) {
            logger.info("部门[{}]及其上级部门没有设置完整的成本标准，无法计算", department.getDepartmentname());
            return;
        }

        Date now = new Date();

        // 部门固定成本
        double departmentFixedCost = 0;
        // 部门一线正式工加班成本
        double departmentFrontlineFormalOverTimeCost = 0;
        //  部门二线加班成本
        double departmentSecondTierOverTimeCost = 0;
        // 部门总工作时长
        double totalNormalWorkHour = 0;

        // 部门加班总时长（小时）
        double totalOvertimeWorkHours = 0;

        // 一线正式员工数
        int frontlineFormalEmployeeNumber = 0;
        // 一线正式员工工作时长
        double frontlineFormalWorkHours = 0;
        // 一线正式员工加班工作时长
        double frontlineFormalOverTimeWorkHours = 0;
        // 一线劳务员工数
        int frontlineLaborEmployeeNumber = 0;
        // 一线劳务员工工作时长
        double frontlineLaborWorkHours = 0;
        // 二线员工数
        int secondTierEmployeeNumber = 0;
        // 二线员工工作时长
        double secondTierWorkHours = 0;
        // 二线员工加班工作时长
        double secondTierOverTimeWorkHours = 0;
        //总夜班补助
        double totalNightAllowance = 0;

        // 一线实际工时统计
        double frontLineActualWorkHours = 0;
        // 二线实际工时统计（排除经理级别以上的工时）
        double secondTierActualWorkHours = 0;

        int formalEmployeeNumber = 0;
        int laborEmployeeNumber = 0;

        // 处理正式员工的考勤
        for (Map.Entry<String, Map<String, Object>> entry : formalAttendanceMapByResourceId.entrySet()) {
            formalEmployeeCount.incrementAndGet();
            formalEmployeeNumber++;
            Map<String, Object> attendance = entry.getValue();
            //工作时长
            double normalWorkHours = Double.parseDouble(attendance.get("sdjccqdkqH").toString());
            //平时加班时长
            double normalOverTimeWorkHours = Double.parseDouble(attendance.get("PSJBH_DEDUCT").toString());
            //周末加班时长
            double weekendOverTimeWorkHours = Double.parseDouble(attendance.get("ZMJBH_DEDUCT").toString());
            //节假日加班时长
            double holidayOverTimeWorkHours = Double.parseDouble(attendance.get("FDJBH_DEDUCT").toString());
            // 夜班补助
            double nightAllowance = Double.parseDouble(attendance.get("paysum_nigt_works").toString());
            totalNightAllowance += nightAllowance;
            // 员工类型
            int level = Integer.parseInt(attendance.get("seclevel").toString());
            if (level <= 5) {
                // 累加一线正式员工数
                frontlineFormalEmployeeNumber++;
                //累加一线正式工工作时长
                frontlineFormalWorkHours += normalWorkHours;
                //累加部门固定成本
                departmentFixedCost += normalWorkHours * frontlineFormal.getAverageCost();
                //累加一线正式工加班时长
                frontlineFormalOverTimeWorkHours += normalOverTimeWorkHours;
                frontlineFormalOverTimeWorkHours += weekendOverTimeWorkHours;
                frontlineFormalOverTimeWorkHours += holidayOverTimeWorkHours;
                //累加一线正式工加班成本
                departmentFrontlineFormalOverTimeCost += holidayOverTimeWorkHours * holidayOverTime.getAverageCost();
                departmentFrontlineFormalOverTimeCost += normalOverTimeWorkHours * normalOverTime.getAverageCost();
                departmentFrontlineFormalOverTimeCost += weekendOverTimeWorkHours * weekendOverTime.getAverageCost();
                // 一线实际工时
                frontLineActualWorkHours += normalWorkHours;
                frontLineActualWorkHours += normalOverTimeWorkHours;
                frontLineActualWorkHours += weekendOverTimeWorkHours;
                frontLineActualWorkHours += holidayOverTimeWorkHours;
            } else {
                // 累加二线正式员工数
                secondTierEmployeeNumber++;
                //累加二线员工工作时长
                secondTierWorkHours += normalWorkHours;
                // 累加部门固定成本
                departmentFixedCost += normalWorkHours * secondTier;
                //累加二线员工加班时长
                secondTierOverTimeWorkHours += normalOverTimeWorkHours;
                secondTierOverTimeWorkHours += weekendOverTimeWorkHours;
                secondTierOverTimeWorkHours += holidayOverTimeWorkHours;
                // 累加二线员工加班成本
                departmentSecondTierOverTimeCost += holidayOverTimeWorkHours * holidayOverTime.getAverageCost();
                departmentSecondTierOverTimeCost += normalOverTimeWorkHours * normalOverTime.getAverageCost();
                departmentSecondTierOverTimeCost += weekendOverTimeWorkHours * weekendOverTime.getAverageCost();
                // 二线实际工时
                if(level < 60){
                    secondTierActualWorkHours += normalWorkHours;
                    secondTierActualWorkHours += normalOverTimeWorkHours;
                    secondTierActualWorkHours += weekendOverTimeWorkHours;
                    secondTierActualWorkHours += holidayOverTimeWorkHours;
                }
            }

            //累加正常工作时长
            totalNormalWorkHour += normalWorkHours;
            // 累加部门总加班时长
            totalOvertimeWorkHours += normalOverTimeWorkHours;
            totalOvertimeWorkHours += weekendOverTimeWorkHours;
            totalOvertimeWorkHours += holidayOverTimeWorkHours;
        }

        //处理劳务员工的考勤
        if (null != laborAttendanceMapByResourceId) {
            for (Map.Entry<String, Map<String, Object>> entry : laborAttendanceMapByResourceId.entrySet()) {
                laborEmployeeCount.incrementAndGet();
                laborEmployeeNumber++;
                Map<String, Object> attendance = entry.getValue();
                //工作时长
                double normalWorkHours = Double.parseDouble(attendance.get("sdjccqdkqH").toString());
                //平时加班时长
                double normalOverTimeWorkHours = Double.parseDouble(attendance.get("PSJBH").toString());
                //周末加班时长
                double weekendOverTimeWorkHours = Double.parseDouble(attendance.get("ZMJBH").toString());
                //节假日加班时长
                double holidayOverTimeWorkHours = Double.parseDouble(attendance.get("FDJBH").toString());
                // 夜班补助
                double nightAllowance = Double.parseDouble(attendance.get("paysum_nigt_works").toString());
                totalNightAllowance += nightAllowance;
                // 劳务工加班也是正常工时
                normalWorkHours += normalOverTimeWorkHours;
                normalOverTimeWorkHours = 0;
                normalWorkHours += weekendOverTimeWorkHours;
                weekendOverTimeWorkHours = 0;
                normalWorkHours += holidayOverTimeWorkHours;
                holidayOverTimeWorkHours = 0;

                // 累加一线劳务员工数
                frontlineLaborEmployeeNumber++;
                //累加一线劳务工工作时长
                frontlineLaborWorkHours += normalWorkHours;
                //累加部门固定成本
                departmentFixedCost += normalWorkHours * frontlineLabor.getAverageCost();

                // 累加到一线实际工时
                frontLineActualWorkHours += normalWorkHours;

                //累加正常工作时长
                totalNormalWorkHour += normalWorkHours;
                // 累加部门总加班时长
                totalOvertimeWorkHours += normalOverTimeWorkHours;
                totalOvertimeWorkHours += weekendOverTimeWorkHours;
                totalOvertimeWorkHours += holidayOverTimeWorkHours;
            }
        }

        DepartmentMonthCost monthCost = new DepartmentMonthCost();

        // 本部门id
        monthCost.setDepartmentId(departmentId);

        // 本部门名称
        monthCost.setDepartmentName(department.getDepartmentname());

        // 设置工作时长（转换为小时）
        monthCost.setNormalWorkHour(totalNormalWorkHour);

        // 设置成本
        monthCost.setFixedCost(departmentFixedCost);
        monthCost.setFrontlineFormalOverTimeCost(departmentFrontlineFormalOverTimeCost);
        monthCost.setSecondTierOverTimeCost(departmentSecondTierOverTimeCost);

        // 一线正式员工人数
        monthCost.setFrontlineFormalEmployeeNumber(frontlineFormalEmployeeNumber);
        // 一线正式员工工作时长
        monthCost.setFrontlineFormalWorkHour(frontlineFormalWorkHours);
        // 一线正式员工加班工作时长
        monthCost.setFrontlineFormalOverTimeWorkHour(frontlineFormalOverTimeWorkHours);

        // 一线劳务员工数
        monthCost.setFrontlineLaborEmployeeNumber(frontlineLaborEmployeeNumber);
        // 一线劳务员工工作时长
        monthCost.setFrontlineLaborWorkHour(frontlineLaborWorkHours);

        // 二线员工数
        monthCost.setSecondTierEmployeeNumber(secondTierEmployeeNumber);
        // 二线员工工作时长
        monthCost.setSecondTierWorkHour(secondTierWorkHours);
        // 二线员工加班工作时长
        monthCost.setSecondTierOverTimeWorkHour(secondTierOverTimeWorkHours);

        // 实际工时
        monthCost.setFrontLineActualWorkHour(frontLineActualWorkHours);
        monthCost.setSecondTierActualWorkHour(secondTierActualWorkHours);

        // 设置日期
        String dateMonth = dateStr.substring(0, 7);
        monthCost.setCostMonth(dateMonth);

        // 设置创建和更新时间
        monthCost.setCreateTime(now);
        monthCost.setUpdateTime(now);

        // 员工加班时长
        monthCost.setOvertimeWorkHour(totalOvertimeWorkHours);

        // 部门夜班补助
        monthCost.setNightAllowance(totalNightAllowance);

        // 出勤率
        monthCost.setAttendanceRate(1.0);

        // 查询是否已存在该部门当月的成本记录，存在则更新
        DepartmentMonthCost existingRecord = DepartmentMonthCostDao.findByDepartmentIdAndDate(departmentId, dateMonth);
        if (null == existingRecord) {
            DepartmentMonthCostDao.insert(monthCost);
        } else {
            // 如果已存在记录，则更新
            logger.info("部门[{}]在月份[{}]已有成本记录，进行更新",
                    department.getDepartmentname(), dateMonth);
            monthCost.setId(existingRecord.getId());
            // 更新记录
            monthCost.setCreateTime(existingRecord.getCreateTime());
            DepartmentMonthCostDao.update(monthCost);
        }
        // 每日记录存入库中，仅插入本月的数据
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String today = sdf.format(now);
        if(dateMonth.equals(today.substring(0, 7))){
            // 查询是否已存在该部门本日的成本记录，存在则更新
            DepartmentMonthCostDailyRecord existingDailyRecord = departmentMonthCostDailyRecordDao.findByDepartmentIdAndMonthDate(departmentId, today, dateMonth);
            if (null == existingDailyRecord) {
                departmentMonthCostDailyRecordDao.insert(convert(monthCost, today));
                logger.info("部门[{}]在月份[{}]日期[{}]没有单日成本记录，插入成功", department.getDepartmentname(), dateMonth, today);
            } else {
                DepartmentMonthCostDailyRecord departmentMonthCostDailyRecord = convert(monthCost, today);
                departmentMonthCostDailyRecord.setId(existingDailyRecord.getId());
                departmentMonthCostDailyRecord.setCreateTime(existingDailyRecord.getCreateTime());
                departmentMonthCostDailyRecordDao.update(departmentMonthCostDailyRecord);
                logger.info("部门[{}]在月份[{}]日期[{}]已有单日成本记录，更新成功", department.getDepartmentname(), dateMonth, today);
            }
        }
        // 更新统计数据
        processedDepartments.incrementAndGet();
        totalCost.addAndGet((int) departmentFixedCost);
        totalCost.addAndGet((int) departmentFrontlineFormalOverTimeCost);
        totalCost.addAndGet((int) departmentSecondTierOverTimeCost);
        totalCost.addAndGet((int) totalNightAllowance);

        logger.info("部门[{}]月成本统计完成，工作时长：{}小时，加班时长：{}小时，成本：{}元，正式员工{}人，劳务工{}人",
                department.getDepartmentname(), totalNormalWorkHour, totalOvertimeWorkHours, departmentFixedCost + departmentFrontlineFormalOverTimeCost + departmentSecondTierOverTimeCost, formalEmployeeNumber, laborEmployeeNumber);
    }

    /**
     * 将DepartmentMonthCost对象转换为DepartmentMonthCostDailyRecord对象
     * 将源对象中的所有字段值复制到目标对象中
     *
     * @param source       DepartmentMonthCost源对象
     * @param calculateDay 计算日期，格式为yyyy-MM-dd
     * @return DepartmentMonthCostDailyRecord对象
     */
    public DepartmentMonthCostDailyRecord convert(DepartmentMonthCost source, String calculateDay) {
        if (source == null) {
            return null;
        }

        DepartmentMonthCostDailyRecord target = new DepartmentMonthCostDailyRecord();

        target.setDepartmentId(source.getDepartmentId());
        target.setDepartmentName(source.getDepartmentName());
        target.setNormalWorkHour(source.getNormalWorkHour());
        target.setOvertimeWorkHour(source.getOvertimeWorkHour());
        target.setFixedCost(source.getFixedCost());
        target.setCostMonth(source.getCostMonth());

        target.setCalculateDay(calculateDay);

        target.setFrontlineFormalEmployeeNumber(source.getFrontlineFormalEmployeeNumber());
        target.setFrontlineFormalWorkHour(source.getFrontlineFormalWorkHour());
        target.setFrontlineFormalOverTimeWorkHour(source.getFrontlineFormalOverTimeWorkHour());
        target.setFrontlineFormalOverTimeCost(source.getFrontlineFormalOverTimeCost());
        target.setFrontlineLaborEmployeeNumber(source.getFrontlineLaborEmployeeNumber());
        target.setFrontlineLaborWorkHour(source.getFrontlineLaborWorkHour());
        target.setSecondTierEmployeeNumber(source.getSecondTierEmployeeNumber());
        target.setSecondTierWorkHour(source.getSecondTierWorkHour());
        target.setSecondTierOverTimeWorkHour(source.getSecondTierOverTimeWorkHour());
        target.setSecondTierOverTimeCost(source.getSecondTierOverTimeCost());
        target.setFrontLineActualWorkHour(source.getFrontLineActualWorkHour());
        target.setSecondTierActualWorkHour(source.getSecondTierActualWorkHour());

        target.setNightAllowance(source.getNightAllowance());
        target.setAttendanceRate(source.getAttendanceRate());

        target.setCreateTime(new Date()); // 使用当前时间作为创建时间
        target.setUpdateTime(new Date()); // 使用当前时间作为更新时间

        return target;
    }
}
