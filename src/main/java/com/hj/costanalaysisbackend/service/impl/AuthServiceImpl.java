package com.hj.costanalaysisbackend.service.impl;

import cn.hutool.crypto.digest.MD5;
import com.hj.costanalaysisbackend.common.ResponseCode;
import com.hj.costanalaysisbackend.dao.sqlserver.HrmresourceDao;
import com.hj.costanalaysisbackend.dto.LoginRequest;
import com.hj.costanalaysisbackend.dto.LoginResponse;
import com.hj.costanalaysisbackend.dto.UserInfo;
import com.hj.costanalaysisbackend.entity.sqlserver.Hrmresource;
import com.hj.costanalaysisbackend.exception.CommonException;
import com.hj.costanalaysisbackend.service.AuthService;
import com.hj.costanalaysisbackend.util.JwtUtil;
import com.hj.costanalaysisbackend.util.UserContext;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * 认证服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class AuthServiceImpl implements AuthService {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthServiceImpl.class);
    
    @Resource
    private HrmresourceDao hrmresourceDao;
    
    @Resource
    private JwtUtil jwtUtil;
    
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        // 参数校验
        if (loginRequest == null || 
            !StringUtils.hasText(loginRequest.getUsername()) || 
            !StringUtils.hasText(loginRequest.getPassword())) {
            throw new CommonException(ResponseCode.USERNAME_OR_PASSWORD_EMPTY.getMsg());
        }
        
        String username = loginRequest.getUsername().trim();
        String password = loginRequest.getPassword().trim();
        
        logger.info("用户{}尝试登录", username);
        
        // 查询用户信息
        Hrmresource user = hrmresourceDao.findByLoginId(username);
        if (user == null) {
            logger.warn("用户{}不存在", username);
            throw new CommonException(ResponseCode.USER_NOT_EXISTS.getMsg());
        }
        
        // 检查用户状态
        if (user.getStatus() == null || user.getStatus() != 1) {
            logger.warn("用户{}状态异常，status: {}", username, user.getStatus());
            throw new CommonException("用户状态异常，无法登录");
        }
        
        // 验证密码
        if (!verifyPassword(password, user.getPassword())) {
            logger.warn("用户{}密码错误", username);
            throw new CommonException(ResponseCode.WRONG_PASSWORD.getMsg());
        }
        
        // 创建用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(user.getId());
        userInfo.setUsername(user.getLoginid());
        userInfo.setRealName(user.getLastname());
        userInfo.setDepartmentId(user.getDepartmentid());
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(userInfo);
        Date expireDate = jwtUtil.getExpirationDateFromToken(token);
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setUserId(user.getId());
        response.setUsername(user.getLoginid());
        response.setRealName(user.getLastname());
        response.setDepartmentId(user.getDepartmentid());
        response.setExpireTime(expireDate != null ? expireDate.getTime() : null);
        
        logger.info("用户{}({})登录成功", user.getLastname(), username);
        
        return response;
    }
    
    @Override
    public UserInfo getCurrentUserInfo() {
        return UserContext.getCurrentUser();
    }
    
    @Override
    public void logout() {
        UserInfo currentUser = UserContext.getCurrentUser();
        if (currentUser != null) {
            logger.info("用户{}({})退出登录", currentUser.getRealName(), currentUser.getUsername());
        }
        UserContext.clear();
    }
    
    /**
     * 验证密码
     * 这里需要根据实际的密码加密方式来实现
     * 
     * @param inputPassword 输入的密码
     * @param storedPassword 存储的密码
     * @return 是否匹配
     */
    private boolean verifyPassword(String inputPassword, String storedPassword) {
        if (!StringUtils.hasText(storedPassword)) {
            return false;
        }
        
        // 这里需要根据实际的密码加密方式来实现
        // 常见的方式有：
        // 1. MD5加密
        // 2. SHA256加密
        // 3. BCrypt加密
        // 4. 明文存储（不推荐）
        
        // 示例：使用MD5加密验证（需要根据实际情况调整）
        String md5Password = MD5.create().digestHex(inputPassword);
        
        // 如果是明文存储，直接比较
        if (inputPassword.equals(storedPassword)) {
            return true;
        }
        
        // 如果是MD5加密存储
        if (md5Password.equalsIgnoreCase(storedPassword)) {
            return true;
        }
        
        // 可以添加其他加密方式的验证
        
        return false;
    }
}
