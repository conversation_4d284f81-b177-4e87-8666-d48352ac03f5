package com.hj.costanalaysisbackend.service;

import com.hj.costanalaysisbackend.dto.LoginRequest;
import com.hj.costanalaysisbackend.dto.LoginResponse;
import com.hj.costanalaysisbackend.dto.UserInfo;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 */
public interface AuthService {
    
    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    UserInfo getCurrentUserInfo();
    
    /**
     * 用户登出
     */
    void logout();
}
