package com.hj.costanalaysisbackend.dto;

import lombok.Data;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class LoginResponse {
    
    /**
     * JWT令牌
     */
    private String token;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 部门ID
     */
    private Integer departmentId;
    
    /**
     * 令牌过期时间（毫秒时间戳）
     */
    private Long expireTime;
}
