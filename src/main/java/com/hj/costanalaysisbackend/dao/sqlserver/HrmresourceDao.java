package com.hj.costanalaysisbackend.dao.sqlserver;

import com.hj.costanalaysisbackend.entity.sqlserver.Hrmresource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HrmresourceDao {

    /**
     * 根据部门ID查询员工
     *
     * @param departmentId 部门ID
     * @return 员工列表
     */
    List<Hrmresource> findByDepartmentId(@Param("departmentId") Integer departmentId);

    List<Hrmresource> getByName(@Param("userName") String userName);

    /**
     * 根据登录ID查询用户
     *
     * @param loginId 登录ID
     * @return 用户信息
     */
    Hrmresource findByLoginId(@Param("loginId") String loginId);

}

