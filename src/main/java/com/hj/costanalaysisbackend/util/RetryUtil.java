package com.hj.costanalaysisbackend.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Supplier;

/**
 * 重试工具类
 * 用于处理可能因为资源死锁等原因失败的操作
 */
public class RetryUtil {

    private static final Logger logger = LoggerFactory.getLogger(RetryUtil.class);

    /**
     * 执行带重试的操作
     *
     * @param operation    要执行的操作
     * @param maxRetries   最大重试次数
     * @param retryDelay   重试间隔（毫秒）
     * @param operationName 操作名称，用于日志记录
     * @param <T>          返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败，抛出最后一次的异常
     */
    public static <T> T executeWithRetry(Supplier<T> operation, int maxRetries, long retryDelay, String operationName) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.debug("执行操作 [{}]，第 {} 次尝试", operationName, attempt);
                T result = operation.get();
                if (attempt > 1) {
                    logger.info("操作 [{}] 在第 {} 次尝试后成功", operationName, attempt);
                }
                return result;
            } catch (Exception e) {
                lastException = e;
                logger.warn("操作 [{}] 第 {} 次尝试失败: {}", operationName, attempt, e.getMessage());
                
                // 如果不是最后一次尝试，则等待后重试
                if (attempt < maxRetries) {
                    try {
                        logger.info("等待 {} 毫秒后进行第 {} 次重试", retryDelay, attempt + 1);
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试过程中被中断", ie);
                    }
                } else {
                    logger.error("操作 [{}] 在 {} 次尝试后仍然失败", operationName, maxRetries);
                }
            }
        }
        
        // 如果所有重试都失败，抛出最后一次的异常
        assert lastException != null;
        throw lastException;
    }

    /**
     * 执行带重试的操作（使用默认参数）
     * 默认最大重试3次，重试间隔2秒
     *
     * @param operation    要执行的操作
     * @param operationName 操作名称，用于日志记录
     * @param <T>          返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败，抛出最后一次的异常
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName) throws Exception {
        return executeWithRetry(operation, 3, 2000L, operationName);
    }

    /**
     * 判断异常是否为数据库死锁相关异常
     *
     * @param exception 异常对象
     * @return 如果是死锁相关异常返回true，否则返回false
     */
    public static boolean isDeadlockException(Exception exception) {
        if (exception == null) {
            return false;
        }
        
        String message = exception.getMessage();
        if (message == null) {
            return false;
        }
        
        // 检查常见的死锁错误信息
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("deadlock") ||
               lowerMessage.contains("死锁") ||
               lowerMessage.contains("lock wait timeout") ||
               lowerMessage.contains("resource busy") ||
               lowerMessage.contains("ora-00060") ||  // Oracle 死锁错误
               lowerMessage.contains("ora-04020") ||  // Oracle 死锁错误
               lowerMessage.contains("1213") ||       // MySQL 死锁错误码
               lowerMessage.contains("1205");         // MySQL 锁等待超时错误码
    }

    /**
     * 执行带重试的操作，只对死锁相关异常进行重试
     *
     * @param operation    要执行的操作
     * @param maxRetries   最大重试次数
     * @param retryDelay   重试间隔（毫秒）
     * @param operationName 操作名称，用于日志记录
     * @param <T>          返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败，抛出最后一次的异常
     */
    public static <T> T executeWithDeadlockRetry(Supplier<T> operation, int maxRetries, long retryDelay, String operationName) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.debug("执行操作 [{}]，第 {} 次尝试", operationName, attempt);
                T result = operation.get();
                if (attempt > 1) {
                    logger.info("操作 [{}] 在第 {} 次尝试后成功", operationName, attempt);
                }
                return result;
            } catch (Exception e) {
                lastException = e;
                
                // 只对死锁相关异常进行重试
                if (!isDeadlockException(e)) {
                    logger.error("操作 [{}] 失败，非死锁异常，不进行重试: {}", operationName, e.getMessage());
                    throw e;
                }
                
                logger.warn("操作 [{}] 第 {} 次尝试因死锁失败: {}", operationName, attempt, e.getMessage());
                
                // 如果不是最后一次尝试，则等待后重试
                if (attempt < maxRetries) {
                    try {
                        logger.info("检测到死锁异常，等待 {} 毫秒后进行第 {} 次重试", retryDelay, attempt + 1);
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试过程中被中断", ie);
                    }
                } else {
                    logger.error("操作 [{}] 在 {} 次尝试后仍然因死锁失败", operationName, maxRetries);
                }
            }
        }
        
        // 如果所有重试都失败，抛出最后一次的异常
        assert lastException != null;
        throw lastException;
    }

    /**
     * 执行带死锁重试的操作（使用默认参数）
     * 默认最大重试3次，重试间隔2秒
     *
     * @param operation    要执行的操作
     * @param operationName 操作名称，用于日志记录
     * @param <T>          返回值类型
     * @return 操作结果
     * @throws Exception 如果所有重试都失败，抛出最后一次的异常
     */
    public static <T> T executeWithDeadlockRetry(Supplier<T> operation, String operationName) throws Exception {
        return executeWithDeadlockRetry(operation, 3, 2000L, operationName);
    }
}
