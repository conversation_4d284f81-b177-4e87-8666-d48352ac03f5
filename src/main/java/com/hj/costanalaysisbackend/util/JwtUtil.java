package com.hj.costanalaysisbackend.util;

import com.hj.costanalaysisbackend.dto.UserInfo;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 */
@Component
public class JwtUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtUtil.class);
    
    /**
     * JWT密钥
     */
    @Value("${jwt.secret:cost-analysis-backend-jwt-secret-key-2024}")
    private String secret;
    
    /**
     * JWT过期时间（毫秒），默认7天
     */
    @Value("${jwt.expiration:604800000}")
    private Long expiration;
    
    /**
     * 生成JWT令牌
     * 
     * @param userInfo 用户信息
     * @return JWT令牌
     */
    public String generateToken(UserInfo userInfo) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);
        
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
        
        return Jwts.builder()
                .setSubject(userInfo.getUserId().toString())
                .claim("username", userInfo.getUsername())
                .claim("realName", userInfo.getRealName())
                .claim("departmentId", userInfo.getDepartmentId())
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
    }
    
    /**
     * 从JWT令牌中获取用户信息
     * 
     * @param token JWT令牌
     * @return 用户信息
     */
    public UserInfo getUserInfoFromToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            UserInfo userInfo = new UserInfo();
            userInfo.setUserId(Integer.valueOf(claims.getSubject()));
            userInfo.setUsername(claims.get("username", String.class));
            userInfo.setRealName(claims.get("realName", String.class));
            userInfo.setDepartmentId(claims.get("departmentId", Integer.class));
            
            return userInfo;
        } catch (Exception e) {
            logger.error("解析JWT令牌失败", e);
            return null;
        }
    }
    
    /**
     * 验证JWT令牌是否有效
     * 
     * @param token JWT令牌
     * @return true表示有效，false表示无效
     */
    public boolean validateToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
            Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            logger.error("JWT签名无效", e);
        } catch (MalformedJwtException e) {
            logger.error("JWT格式错误", e);
        } catch (ExpiredJwtException e) {
            logger.error("JWT已过期", e);
        } catch (UnsupportedJwtException e) {
            logger.error("不支持的JWT", e);
        } catch (IllegalArgumentException e) {
            logger.error("JWT参数为空", e);
        }
        return false;
    }
    
    /**
     * 获取JWT令牌的过期时间
     * 
     * @param token JWT令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            return claims.getExpiration();
        } catch (Exception e) {
            logger.error("获取JWT过期时间失败", e);
            return null;
        }
    }
}
