package com.hj.costanalaysisbackend.util;

import com.hj.costanalaysisbackend.dto.UserInfo;

/**
 * 用户上下文工具类
 * 用于在当前线程中存储和获取用户信息
 * 
 * <AUTHOR>
 */
public class UserContext {
    
    private static final ThreadLocal<UserInfo> userThreadLocal = new ThreadLocal<>();
    
    /**
     * 设置当前用户信息
     * 
     * @param userInfo 用户信息
     */
    public static void setCurrentUser(UserInfo userInfo) {
        userThreadLocal.set(userInfo);
    }
    
    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    public static UserInfo getCurrentUser() {
        return userThreadLocal.get();
    }
    
    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    public static Integer getCurrentUserId() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getUserId() : null;
    }
    
    /**
     * 获取当前用户名
     * 
     * @return 用户名
     */
    public static String getCurrentUsername() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getUsername() : null;
    }
    
    /**
     * 获取当前用户真实姓名
     * 
     * @return 真实姓名
     */
    public static String getCurrentRealName() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getRealName() : null;
    }
    
    /**
     * 获取当前用户部门ID
     * 
     * @return 部门ID
     */
    public static Integer getCurrentDepartmentId() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getDepartmentId() : null;
    }
    
    /**
     * 清除当前用户信息
     */
    public static void clear() {
        userThreadLocal.remove();
    }
}
