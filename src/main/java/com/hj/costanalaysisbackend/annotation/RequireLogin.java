package com.hj.costanalaysisbackend.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 需要登录才能访问的注解
 * 可以用在类或方法上
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireLogin {
    
    /**
     * 是否需要登录，默认为true
     * @return true表示需要登录，false表示不需要登录
     */
    boolean value() default true;
}
