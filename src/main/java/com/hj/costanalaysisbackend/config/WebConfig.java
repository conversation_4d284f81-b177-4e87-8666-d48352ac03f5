package com.hj.costanalaysisbackend.config;

import com.hj.costanalaysisbackend.interceptor.LoginInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private LoginInterceptor loginInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                        "/auth/login",      // 排除登录接口
                        "/error",           // 排除错误页面
                        "/favicon.ico",     // 排除图标
                        "/swagger-ui/**",   // 排除Swagger UI（如果有的话）
                        "/v3/api-docs/**",  // 排除API文档（如果有的话）
                        "/actuator/**"      // 排除监控端点（如果有的话）
                );
    }
}
